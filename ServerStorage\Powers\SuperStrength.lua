-- SuperStrength module (server)
local Config = require(game.ReplicatedStorage.SupermanShared.Config)

return function(player, mouseHit)
    local char = player.Character
    if not char then return end
    local root = char:Find<PERSON>irs<PERSON><PERSON>hild("HumanoidRootPart")
    local humanoid = char:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("Humanoid")
    if not root or humanoid.Health <= 0 then return end

    -- Punch in direction of mouse
    local direction = (mouseHit - root.Position).Unit
    local range = 10
    
    -- Find targets in punch range
    for _, otherPlayer in pairs(game.Players:GetPlayers()) do
        if otherPlayer ~= player and otherPlayer.Character then
            local otherRoot = otherPlayer.Character:FindFirstChild("HumanoidRootPart")
            local otherHumanoid = otherPlayer.Character:FindFirstChild("Humanoid")
            
            if otherRoot and otherHumanoid then
                local distance = (otherRoot.Position - root.Position).Magnitude
                
                if distance <= range then
                    -- Damage
                    otherHumanoid:TakeDamage(Config.POWERS.SuperStrength.dmg)
                    
                    -- Knockback
                    local bodyVelocity = Instance.new("BodyVelocity")
                    bodyVelocity.MaxForce = Vector3.new(400000, 400000, 400000)
                    bodyVelocity.Velocity = direction * 100
                    bodyVelocity.Parent = otherRoot
                    
                    -- Remove knockback after short time
                    game:GetService("Debris"):AddItem(bodyVelocity, 0.5)
                end
            end
        end
    end

    -- Visual effect
    local remote = game.ReplicatedStorage.SupermanShared.PowerRemote
    remote:FireAllClients("SuperStrength", root.Position, mouseHit)
end
