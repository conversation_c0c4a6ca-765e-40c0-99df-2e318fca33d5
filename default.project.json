{"name": "<PERSON><PERSON><PERSON><PERSON>", "tree": {"$className": "DataModel", "ReplicatedStorage": {"$className": "ReplicatedStorage", "SupermanShared": {"$className": "Folder", "Config": {"$path": "ReplicatedStorage/SupermanShared/Config.lua"}, "FlightRemote": {"$className": "RemoteEvent"}, "PowerRemote": {"$className": "RemoteEvent"}}}, "ServerScriptService": {"$className": "ServerScriptService", "SupermanMain": {"$path": "ServerScriptService/SupermanMain.lua"}}, "ServerStorage": {"$className": "ServerStorage", "Powers": {"$className": "Folder", "HeatVision": {"$path": "ServerStorage/Powers/HeatVision.lua"}, "FreezeBreath": {"$path": "ServerStorage/Powers/FreezeBreath.lua"}, "SuperStrength": {"$path": "ServerStorage/Powers/SuperStrength.lua"}}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "ClientFlight": {"$path": "StarterPlayer/StarterPlayerScripts/ClientFlight.lua"}}}}}